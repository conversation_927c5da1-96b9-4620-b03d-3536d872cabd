#!/bin/bash

# Redis Data Inspection Script for ChatAI Service
# Usage: ./scripts/inspect-redis.sh [command] [key]

REDIS_PASSWORD="chatai_redis_2024"
CONTAINER_NAME="chatai_redis"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to execute Redis command
redis_exec() {
    docker exec $CONTAINER_NAME redis-cli -a $REDIS_PASSWORD "$@" 2>/dev/null
}

# Function to show help
show_help() {
    echo -e "${CYAN}Redis Data Inspection Tool for ChatAI Service${NC}"
    echo -e "${YELLOW}Usage: $0 [command] [options]${NC}"
    echo ""
    echo -e "${GREEN}Commands:${NC}"
    echo "  keys [pattern]     - List all keys (default: chatai:*)"
    echo "  sessions          - List all session keys"
    echo "  contexts          - List all context cache keys"
    echo "  apikeys           - List all API key cache keys"
    echo "  apps              - List all app mapping keys"
    echo "  get <key>         - Get value of specific key (formatted JSON)"
    echo "  ttl <key>         - Get TTL (time to live) of key"
    echo "  stats             - Show Redis statistics"
    echo "  info              - Show Redis info"
    echo "  monitor           - Monitor Redis commands in real-time"
    echo "  clear [pattern]   - Clear keys matching pattern (default: chatai:*)"
    echo "  count             - Count keys by type"
    echo ""
    echo -e "${GREEN}Examples:${NC}"
    echo "  $0 keys                    # List all chatai keys"
    echo "  $0 sessions               # List all session keys"
    echo "  $0 get chatai:session:123 # Get specific session data"
    echo "  $0 ttl chatai:session:123 # Check TTL for session"
    echo "  $0 stats                  # Show database statistics"
}

# Function to list keys by pattern
list_keys() {
    local pattern=${1:-"chatai:*"}
    echo -e "${CYAN}Keys matching pattern: ${YELLOW}$pattern${NC}"
    redis_exec keys "$pattern" | sort
}

# Function to list sessions
list_sessions() {
    echo -e "${CYAN}Session Keys:${NC}"
    redis_exec keys "chatai:session:*" | sort
    echo ""
    echo -e "${YELLOW}Session Count:${NC} $(redis_exec keys "chatai:session:*" | wc -l)"
}

# Function to list contexts
list_contexts() {
    echo -e "${CYAN}Context Cache Keys:${NC}"
    redis_exec keys "chatai:context:*" | sort
    echo ""
    echo -e "${YELLOW}Context Count:${NC} $(redis_exec keys "chatai:context:*" | wc -l)"
}

# Function to list API keys
list_apikeys() {
    echo -e "${CYAN}API Key Cache Keys:${NC}"
    redis_exec keys "chatai:apikey:*" | sort
    echo ""
    echo -e "${YELLOW}API Key Count:${NC} $(redis_exec keys "chatai:apikey:*" | wc -l)"
}

# Function to list apps
list_apps() {
    echo -e "${CYAN}App Mapping Keys:${NC}"
    redis_exec keys "chatai:app:*" | sort
    echo ""
    echo -e "${YELLOW}App Count:${NC} $(redis_exec keys "chatai:app:*" | wc -l)"
}

# Function to get key value
get_key() {
    local key=$1
    if [ -z "$key" ]; then
        echo -e "${RED}Error: Key is required${NC}"
        return 1
    fi
    
    echo -e "${CYAN}Key: ${YELLOW}$key${NC}"
    echo -e "${CYAN}TTL: ${YELLOW}$(redis_exec ttl "$key") seconds${NC}"
    echo -e "${CYAN}Value:${NC}"
    redis_exec get "$key" | jq . 2>/dev/null || redis_exec get "$key"
}

# Function to get TTL
get_ttl() {
    local key=$1
    if [ -z "$key" ]; then
        echo -e "${RED}Error: Key is required${NC}"
        return 1
    fi
    
    local ttl=$(redis_exec ttl "$key")
    echo -e "${CYAN}Key: ${YELLOW}$key${NC}"
    if [ "$ttl" -eq -1 ]; then
        echo -e "${YELLOW}TTL: No expiration set${NC}"
    elif [ "$ttl" -eq -2 ]; then
        echo -e "${RED}TTL: Key does not exist${NC}"
    else
        echo -e "${YELLOW}TTL: $ttl seconds ($(($ttl / 60)) minutes)${NC}"
    fi
}

# Function to show stats
show_stats() {
    echo -e "${CYAN}Redis Database Statistics:${NC}"
    redis_exec info keyspace
    echo ""
    echo -e "${CYAN}Memory Usage:${NC}"
    redis_exec info memory | grep -E "used_memory_human|used_memory_peak_human"
    echo ""
    echo -e "${CYAN}Key Counts by Type:${NC}"
    echo -e "${YELLOW}Sessions:${NC} $(redis_exec keys "chatai:session:*" | wc -l)"
    echo -e "${YELLOW}Contexts:${NC} $(redis_exec keys "chatai:context:*" | wc -l)"
    echo -e "${YELLOW}API Keys:${NC} $(redis_exec keys "chatai:apikey:*" | wc -l)"
    echo -e "${YELLOW}Apps:${NC} $(redis_exec keys "chatai:app:*" | wc -l)"
    echo -e "${YELLOW}Total ChatAI Keys:${NC} $(redis_exec keys "chatai:*" | wc -l)"
}

# Function to show info
show_info() {
    echo -e "${CYAN}Redis Server Information:${NC}"
    redis_exec info server
}

# Function to monitor Redis
monitor_redis() {
    echo -e "${CYAN}Monitoring Redis commands (Press Ctrl+C to stop):${NC}"
    redis_exec monitor
}

# Function to clear keys
clear_keys() {
    local pattern=${1:-"chatai:*"}
    echo -e "${YELLOW}Warning: This will delete all keys matching pattern: $pattern${NC}"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        local keys=$(redis_exec keys "$pattern")
        if [ -n "$keys" ]; then
            echo "$keys" | xargs -r docker exec $CONTAINER_NAME redis-cli -a $REDIS_PASSWORD del
            echo -e "${GREEN}Cleared keys matching: $pattern${NC}"
        else
            echo -e "${YELLOW}No keys found matching: $pattern${NC}"
        fi
    else
        echo -e "${YELLOW}Operation cancelled${NC}"
    fi
}

# Function to count keys
count_keys() {
    echo -e "${CYAN}Key Count Summary:${NC}"
    echo -e "${YELLOW}Sessions:${NC} $(redis_exec keys "chatai:session:*" | wc -l)"
    echo -e "${YELLOW}Contexts:${NC} $(redis_exec keys "chatai:context:*" | wc -l)"
    echo -e "${YELLOW}API Keys:${NC} $(redis_exec keys "chatai:apikey:*" | wc -l)"
    echo -e "${YELLOW}Apps:${NC} $(redis_exec keys "chatai:app:*" | wc -l)"
    echo -e "${PURPLE}Total ChatAI Keys:${NC} $(redis_exec keys "chatai:*" | wc -l)"
    echo -e "${PURPLE}Total All Keys:${NC} $(redis_exec dbsize)"
}

# Main script logic
case "$1" in
    "keys")
        list_keys "$2"
        ;;
    "sessions")
        list_sessions
        ;;
    "contexts")
        list_contexts
        ;;
    "apikeys")
        list_apikeys
        ;;
    "apps")
        list_apps
        ;;
    "get")
        get_key "$2"
        ;;
    "ttl")
        get_ttl "$2"
        ;;
    "stats")
        show_stats
        ;;
    "info")
        show_info
        ;;
    "monitor")
        monitor_redis
        ;;
    "clear")
        clear_keys "$2"
        ;;
    "count")
        count_keys
        ;;
    "help"|"-h"|"--help"|"")
        show_help
        ;;
    *)
        echo -e "${RED}Unknown command: $1${NC}"
        show_help
        exit 1
        ;;
esac
