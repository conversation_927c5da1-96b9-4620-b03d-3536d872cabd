const { createClient } = require('redis');

class RedisService {
  constructor() {
    this.client = null;
    this.isConnected = false;
    this.connectionAttempts = 0;
    this.maxRetries = 3;
    this.keyPrefix = process.env.REDIS_KEY_PREFIX || 'chatai:';

    // Configuration
    this.config = {
      url: process.env.REDIS_URL || 'redis://localhost:6379',
      password: '',
      database: parseInt(process.env.REDIS_DB) || 0,
      socket: {
        connectTimeout: parseInt(process.env.REDIS_CONNECTION_TIMEOUT) || 5000,
        commandTimeout: parseInt(process.env.REDIS_COMMAND_TIMEOUT) || 3000,
        reconnectStrategy: (retries) => {
          if (retries > 10) {
            console.error('❌ Redis: Too many reconnection attempts, giving up');
            return new Error('Too many reconnection attempts');
          }
          return Math.min(retries * 100, 3000);
        }
      }
    };

    // Initialize connection asynchronously (non-blocking)
    this.initialize().catch(err => {
      console.error('❌ Redis initialization failed:', err.message);
    });
  }

  /**
   * Initialize Redis connection
   */
  async initialize() {
    try {
      console.log('🔌 Initializing Redis connection...');

      this.client = createClient(this.config);

      // Event handlers
      this.client.on('error', (err) => {
        console.error('❌ Redis Client Error:', err.message);
        this.isConnected = false;
      });

      this.client.on('connect', () => {
        console.log('🔗 Redis Client connecting...');
      });

      this.client.on('ready', () => {
        console.log('✅ Redis Client ready and connected');
        this.isConnected = true;
        this.connectionAttempts = 0;
      });

      this.client.on('end', () => {
        console.log('🔌 Redis Client connection ended');
        this.isConnected = false;
      });

      this.client.on('reconnecting', () => {
        console.log('🔄 Redis Client reconnecting...');
      });

      // Connect to Redis
      await this.client.connect();

      // Test connection
      await this.testConnection();

      console.log('✅ Redis connection established successfully');

    } catch (error) {
      this.connectionAttempts++;
      console.error(`❌ Redis connection failed (attempt ${this.connectionAttempts}/${this.maxRetries}):`, error.message);

      if (this.connectionAttempts < this.maxRetries) {
        console.log(`🔄 Retrying Redis connection in 3 seconds...`);
        setTimeout(() => this.initialize(), 3000);
      } else {
        console.error(`❌ Failed to connect to Redis after ${this.maxRetries} attempts`);
        // Don't throw error, allow graceful fallback to in-memory caching
      }
    }
  }

  /**
   * Test Redis connection
   */
  async testConnection() {
    if (!this.isConnected || !this.client) {
      throw new Error('Redis not connected');
    }

    const testKey = `${this.keyPrefix}test:${Date.now()}`;
    await this.client.set(testKey, 'test', { EX: 10 });
    const result = await this.client.get(testKey);
    await this.client.del(testKey);

    if (result !== 'test') {
      throw new Error('Redis test failed');
    }

    console.log('✅ Redis connection test passed');
  }

  /**
   * Generate prefixed key
   */
  getKey(key) {
    return `${this.keyPrefix}${key}`;
  }

  /**
   * Check if Redis is available
   */
  isAvailable() {
    return this.isConnected && this.client;
  }

  /**
   * Set a key with optional TTL
   */
  async set(key, value, ttlSeconds = null) {
    if (!this.isAvailable()) {
      throw new Error('Redis not available');
    }

    const prefixedKey = this.getKey(key);
    const serializedValue = JSON.stringify(value);

    if (ttlSeconds) {
      await this.client.set(prefixedKey, serializedValue, { EX: ttlSeconds });
    } else {
      await this.client.set(prefixedKey, serializedValue);
    }
  }

  /**
   * Get a key
   */
  async get(key) {
    if (!this.isAvailable()) {
      throw new Error('Redis not available');
    }

    const prefixedKey = this.getKey(key);
    const value = await this.client.get(prefixedKey);

    if (value === null) {
      return null;
    }

    try {
      return JSON.parse(value);
    } catch (error) {
      console.error(`❌ Failed to parse Redis value for key ${key}:`, error.message);
      return null;
    }
  }

  /**
   * Delete a key
   */
  async del(key) {
    if (!this.isAvailable()) {
      throw new Error('Redis not available');
    }

    const prefixedKey = this.getKey(key);
    return await this.client.del(prefixedKey);
  }

  /**
   * Check if key exists
   */
  async exists(key) {
    if (!this.isAvailable()) {
      throw new Error('Redis not available');
    }

    const prefixedKey = this.getKey(key);
    return await this.client.exists(prefixedKey);
  }

  /**
   * Set TTL for existing key
   */
  async expire(key, ttlSeconds) {
    if (!this.isAvailable()) {
      throw new Error('Redis not available');
    }

    const prefixedKey = this.getKey(key);
    return await this.client.expire(prefixedKey, ttlSeconds);
  }

  /**
   * Get TTL for key
   */
  async ttl(key) {
    if (!this.isAvailable()) {
      throw new Error('Redis not available');
    }

    const prefixedKey = this.getKey(key);
    return await this.client.ttl(prefixedKey);
  }

  /**
   * Increment a counter
   */
  async incr(key, ttlSeconds = null) {
    if (!this.isAvailable()) {
      throw new Error('Redis not available');
    }

    const prefixedKey = this.getKey(key);
    const result = await this.client.incr(prefixedKey);

    if (ttlSeconds && result === 1) {
      // Set TTL only on first increment
      await this.client.expire(prefixedKey, ttlSeconds);
    }

    return result;
  }

  /**
   * Get multiple keys
   */
  async mget(keys) {
    if (!this.isAvailable()) {
      throw new Error('Redis not available');
    }

    const prefixedKeys = keys.map(key => this.getKey(key));
    const values = await this.client.mGet(prefixedKeys);

    return values.map(value => {
      if (value === null) return null;
      try {
        return JSON.parse(value);
      } catch (error) {
        console.error(`❌ Failed to parse Redis value:`, error.message);
        return null;
      }
    });
  }

  /**
   * Set multiple keys
   */
  async mset(keyValuePairs, ttlSeconds = null) {
    if (!this.isAvailable()) {
      throw new Error('Redis not available');
    }

    const pipeline = this.client.multi();

    for (const [key, value] of Object.entries(keyValuePairs)) {
      const prefixedKey = this.getKey(key);
      const serializedValue = JSON.stringify(value);

      if (ttlSeconds) {
        pipeline.set(prefixedKey, serializedValue, { EX: ttlSeconds });
      } else {
        pipeline.set(prefixedKey, serializedValue);
      }
    }

    await pipeline.exec();
  }

  /**
   * Get keys matching pattern
   */
  async keys(pattern) {
    if (!this.isAvailable()) {
      throw new Error('Redis not available');
    }

    const prefixedPattern = this.getKey(pattern);
    const keys = await this.client.keys(prefixedPattern);

    // Remove prefix from returned keys
    return keys.map(key => key.replace(this.keyPrefix, ''));
  }

  /**
   * Clear all keys with prefix
   */
  async flushPrefix(prefix = '') {
    if (!this.isAvailable()) {
      throw new Error('Redis not available');
    }

    const pattern = this.getKey(`${prefix}*`);
    const keys = await this.client.keys(pattern);

    if (keys.length > 0) {
      await this.client.del(keys);
      console.log(`🗑️ Cleared ${keys.length} Redis keys with pattern: ${pattern}`);
    }

    return keys.length;
  }

  /**
   * Get Redis info and stats
   */
  async getStats() {
    if (!this.isAvailable()) {
      return {
        connected: false,
        error: 'Redis not available'
      };
    }

    try {
      const info = await this.client.info('memory');
      const dbSize = await this.client.dbSize();

      return {
        connected: true,
        database: this.config.database,
        keyPrefix: this.keyPrefix,
        totalKeys: dbSize,
        memoryInfo: this.parseRedisInfo(info)
      };
    } catch (error) {
      return {
        connected: false,
        error: error.message
      };
    }
  }

  /**
   * Parse Redis INFO output
   */
  parseRedisInfo(info) {
    const lines = info.split('\r\n');
    const result = {};

    for (const line of lines) {
      if (line.includes(':')) {
        const [key, value] = line.split(':');
        result[key] = value;
      }
    }

    return result;
  }

  /**
   * Close Redis connection
   */
  async close() {
    if (this.client) {
      await this.client.quit();
      this.isConnected = false;
      console.log('🔌 Redis connection closed');
    }
  }

  /**
   * Get connection status
   */
  getStatus() {
    return {
      connected: this.isConnected,
      attempts: this.connectionAttempts,
      maxRetries: this.maxRetries,
      config: {
        url: this.config.url,
        database: this.config.database,
        keyPrefix: this.keyPrefix
      }
    };
  }
}

// Export singleton instance
module.exports = new RedisService();
